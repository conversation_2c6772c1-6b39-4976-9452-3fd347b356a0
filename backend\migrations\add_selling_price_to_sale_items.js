// Migration to add selling_price column to sale_items table
const pool = require('../database');

async function addSellingPriceColumn() {
  try {
    console.log('Adding selling_price column to sale_items table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sale_items' AND column_name = 'selling_price'
    `;
    
    const { rows: existingColumns } = await pool.query(checkColumnQuery);
    
    if (existingColumns.length > 0) {
      console.log('selling_price column already exists in sale_items table');
      return;
    }
    
    // Add the selling_price column
    const addColumnQuery = `
      ALTER TABLE sale_items 
      ADD COLUMN selling_price DECIMAL(10, 2) DEFAULT 0.00
    `;
    
    await pool.query(addColumnQuery);
    console.log('Successfully added selling_price column to sale_items table');
    
    // Update existing records to set selling_price = unit_price_excluding_tax for backward compatibility
    const updateExistingQuery = `
      UPDATE sale_items 
      SET selling_price = unit_price_excluding_tax 
      WHERE selling_price = 0.00 OR selling_price IS NULL
    `;
    
    const { rowCount } = await pool.query(updateExistingQuery);
    console.log(`Updated ${rowCount} existing records with selling_price = unit_price_excluding_tax`);
    
  } catch (error) {
    console.error('Error adding selling_price column:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  addSellingPriceColumn()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = addSellingPriceColumn;
