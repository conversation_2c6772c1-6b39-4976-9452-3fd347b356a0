// Analyze Current Database Script
// Examines the current Render.com database structure and data

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function analyzeDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Analyzing current database structure and data...\n');
    
    // Get all tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('📋 Tables found:');
    for (const row of tablesResult.rows) {
      console.log('  -', row.table_name);
    }
    
    console.log('\n📊 Data volume analysis:');
    
    const tables = [
      'users', 'rooms', 'expense_categories', 'categories', 'car_brands', 
      'car_models', 'products', 'sales', 'sale_items', 'expenses', 
      'quotations', 'quotation_items', 'invoices', 'invoice_items'
    ];
    
    let totalRecords = 0;
    
    for (const table of tables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
        const count = parseInt(result.rows[0].count);
        totalRecords += count;
        console.log(`  ${table}: ${count} records`);
      } catch (error) {
        console.log(`  ${table}: Table not found or error - ${error.message}`);
      }
    }
    
    console.log(`\n📈 Total records across all tables: ${totalRecords}`);
    
    console.log('\n🔗 Checking foreign key relationships:');
    const fkResult = await client.query(`
      SELECT 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE constraint_type = 'FOREIGN KEY'
      ORDER BY tc.table_name;
    `);
    
    for (const row of fkResult.rows) {
      console.log(`  ${row.table_name}.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name}`);
    }
    
    console.log('\n🔑 Checking sequences:');
    const sequencesResult = await client.query(`
      SELECT sequence_name 
      FROM information_schema.sequences 
      WHERE sequence_schema = 'public'
      ORDER BY sequence_name
    `);
    
    for (const row of sequencesResult.rows) {
      const seqResult = await client.query(`SELECT last_value FROM ${row.sequence_name}`);
      console.log(`  ${row.sequence_name}: last_value = ${seqResult.rows[0].last_value}`);
    }
    
    console.log('\n✅ Database analysis completed successfully');
    
  } catch (error) {
    console.error('❌ Error analyzing database:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the analysis
analyzeDatabase().catch(console.error);
