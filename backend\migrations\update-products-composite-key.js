// Migration script to update products table to allow same item_code in different rooms
const pool = require('../database');

async function updateProductsSchema() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Starting migration to allow same item codes in different rooms...');
    
    // Step 1: Check if the table exists and has the old structure
    const tableCheck = await client.query(`
      SELECT column_name, is_nullable, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'products' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    if (tableCheck.rows.length === 0) {
      console.log('Products table does not exist. Creating with new schema...');
      
      // Create the table with the new composite primary key
      await client.query(`
        CREATE TABLE products (
          item_code VARCHAR(100) NOT NULL,
          room_id INTEGER NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          car_brand VARCHAR(100) NOT NULL,
          car_model VARCHAR(100) NOT NULL,
          unit_retail_price DECIMAL(10,2) NOT NULL,
          wholesale_price DECIMAL(10,2) NOT NULL,
          unit_cost DECIMAL(10,2) NOT NULL,
          supplier_code VARCHAR(100) NOT NULL,
          available_stock INTEGER NOT NULL,
          location VARCHAR(255) NOT NULL,
          colour_tape VARCHAR(50) NOT NULL,
          profit DECIMAL(10,2) NOT NULL,
          additional_comments TEXT,
          product_category VARCHAR(100) NOT NULL,
          min_order_quantity INTEGER DEFAULT 1,
          low_stock_threshold INTEGER DEFAULT 5,
          PRIMARY KEY (item_code, room_id),
          FOREIGN KEY(room_id) REFERENCES rooms(id) ON DELETE CASCADE
        )
      `);
      
      console.log('Products table created with composite primary key (item_code, room_id)');
    } else {
      console.log('Products table exists. Checking current structure...');
      
      // Check if we already have the composite primary key
      const constraintCheck = await client.query(`
        SELECT constraint_name, constraint_type
        FROM information_schema.table_constraints 
        WHERE table_name = 'products' AND table_schema = 'public' AND constraint_type = 'PRIMARY KEY'
      `);
      
      if (constraintCheck.rows.length > 0) {
        const pkColumns = await client.query(`
          SELECT column_name
          FROM information_schema.key_column_usage
          WHERE table_name = 'products' AND table_schema = 'public' 
          AND constraint_name = $1
          ORDER BY ordinal_position
        `, [constraintCheck.rows[0].constraint_name]);
        
        const currentPkColumns = pkColumns.rows.map(row => row.column_name);
        
        if (currentPkColumns.length === 2 && currentPkColumns.includes('item_code') && currentPkColumns.includes('room_id')) {
          console.log('Products table already has the correct composite primary key. No migration needed.');
          await client.query('COMMIT');
          return;
        }
        
        console.log('Current primary key columns:', currentPkColumns);
        console.log('Updating to composite primary key...');
        
        // Step 2: Create a backup table
        console.log('Creating backup table...');
        await client.query(`
          CREATE TABLE products_backup AS SELECT * FROM products
        `);
        
        // Step 3: Drop the existing primary key constraint
        console.log('Dropping existing primary key constraint...');
        await client.query(`
          ALTER TABLE products DROP CONSTRAINT ${constraintCheck.rows[0].constraint_name}
        `);
        
        // Step 4: Add the new composite primary key
        console.log('Adding composite primary key (item_code, room_id)...');
        await client.query(`
          ALTER TABLE products ADD CONSTRAINT products_pkey PRIMARY KEY (item_code, room_id)
        `);
        
        console.log('Migration completed successfully!');
      }
    }
    
    // Verify the final structure
    const finalCheck = await client.query(`
      SELECT column_name
      FROM information_schema.key_column_usage
      WHERE table_name = 'products' AND table_schema = 'public' 
      AND constraint_name = 'products_pkey'
      ORDER BY ordinal_position
    `);
    
    console.log('Final primary key columns:', finalCheck.rows.map(row => row.column_name));
    
    await client.query('COMMIT');
    console.log('Migration completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  updateProductsSchema()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { updateProductsSchema };
