<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <!-- Fixed viewport meta tag to prevent mobile scaling issues -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <!-- Add performance-focused meta tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="theme-color" content="#ffffff">
  <meta name="format-detection" content="telephone=no">
  <title>Shans System - Receipt</title>
  <!-- Add performance optimization files -->
  <link rel="stylesheet" href="Assets/performance.css">
  <script src="Assets/performance.js"></script>
  <!-- Add authentication utilities -->
  <script src="auth-utils.js"></script>
  <style>
    /* Basic Reset */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    /* Body Styling */
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f4f4f4;
      padding: 20px;
    }

    /* Responsive Container */
    .container {
      width: 100%;
      max-width: 800px; /* Approximately A4 width */
      margin: auto;
      background: white;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      position: relative;
    }

    /* Container wrapper */
    .container-wrapper {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: flex-start;
    }

    /* Header */
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .logo {
      width: 150px;
      height: auto;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .logo img {
      max-width: 100%;
      max-height: 100px;
      object-fit: contain;
      width: auto;
      height: auto;
    }

    /* Date Section Styles */
    .date-section {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .date-display {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .date-edit-btn {
      padding: 6px 12px;
      font-size: 12px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      min-height: 32px;
      min-width: 50px;
    }

    .date-edit-btn:hover {
      background: #0056b3;
    }

    .date-edit-btn:focus {
      outline: 2px solid #007bff;
      outline-offset: 2px;
    }

    .date-edit-controls {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
      max-width: 250px;
    }

    .date-input {
      padding: 8px 12px;
      font-size: 14px;
      border: 2px solid #ddd;
      border-radius: 4px;
      width: 100%;
      min-height: 40px;
      transition: border-color 0.2s ease;
    }

    .date-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .date-edit-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .date-save-btn, .date-cancel-btn {
      padding: 8px 16px;
      font-size: 13px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      min-height: 36px;
      min-width: 70px;
      flex: 1;
    }

    .date-save-btn {
      background: #28a745;
      color: white;
    }

    .date-save-btn:hover {
      background: #218838;
    }

    .date-save-btn:focus {
      outline: 2px solid #28a745;
      outline-offset: 2px;
    }

    .date-cancel-btn {
      background: #6c757d;
      color: white;
    }

    .date-cancel-btn:hover {
      background: #5a6268;
    }

    .date-cancel-btn:focus {
      outline: 2px solid #6c757d;
      outline-offset: 2px;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .date-section {
        width: 100%;
      }

      .date-edit-controls {
        max-width: 100%;
      }

      .date-input {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 44px; /* Better touch target */
      }

      .date-edit-btn, .date-save-btn, .date-cancel-btn {
        min-height: 44px; /* Better touch targets */
        font-size: 14px;
      }

      .date-edit-buttons {
        flex-direction: column;
      }

      .date-save-btn, .date-cancel-btn {
        flex: none;
        width: 100%;
      }
    }

    /* Screenshot utility class */
    .hide-for-screenshot {
      display: none !important;
    }

    h1 {
      text-align: center;
      flex-grow: 1;
      margin: 0 20px;
      font-size: 32px;
    }

    /* Info Container for mobile layout */
    .info-container {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    /* Company Details */
    .company-details {
      margin-bottom: 20px;
    }

    .company-details h2 {
      margin-bottom: 10px;
      font-size: 24px;
    }

    .address-contact {
      margin-bottom: 10px;
    }

    /* Reference Number */
    .reference-number {
      margin-top: 10px;
      font-size: 16px;
      font-weight: bold;
    }

    /* Editable Reference Number */
    .reference-number-container {
      margin-top: 10px;
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }

    .reference-number-display {
      font-size: 16px;
      font-weight: bold;
    }

    .reference-number-edit {
      display: flex;
      align-items: center;
      gap: 5px;
      flex-wrap: wrap;
    }

    .reference-number-edit label {
      font-size: 14px;
      font-weight: bold;
    }

    .reference-number-input {
      padding: 4px 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
      width: 120px;
    }

    .ref-btn {
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      min-width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ref-edit-btn {
      background-color: transparent;
      color: #666;
      border: 1px solid transparent;
    }

    .ref-edit-btn:hover {
      background-color: #f8f9fa;
      color: #333;
      border: 1px solid #ddd;
    }

    .ref-save-btn {
      background-color: #28a745;
      color: white;
    }

    .ref-save-btn:hover {
      background-color: #1e7e34;
    }

    .ref-cancel-btn {
      background-color: #dc3545;
      color: white;
    }

    .ref-cancel-btn:hover {
      background-color: #c82333;
    }

    /* Billing and Shipping */
    .billing-shipping {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .billing, .shipping {
      flex-basis: 48%;
      margin-bottom: 20px;
    }

    .billing h3, .shipping h3 {
      margin-bottom: 10px;
      font-size: 20px;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }

    /* Payment Method Section */
    .payment-method {
      margin-bottom: 20px;
      font-size: 16px;
    }

    /* Table Container Styling */
    .table-container {
      width: 100%;
      margin-bottom: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
      overflow-x: auto; /* Allow horizontal scrolling on desktop */
    }

    /* Div-based Table Styling */
    .div-table {
      display: flex;
      flex-direction: column;
      width: 100%;
      background-color: white;
      font-size: 16px; /* Increased from 14px to 16px (+2px) */
      border: 1px solid #e0e0e0;
      margin-bottom: 0;
    }

    .div-table-row {
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #e0e0e0;
    }

    .div-table-row:nth-child(even) {
      background-color: #f9f9f9;
    }

    .div-table-row:hover {
      background-color: #f0f7ff;
    }

    .div-table-header {
      background-color: #f2f2f2;
      font-weight: bold;
      border-bottom: 2px solid #d0d0d0;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .div-table-cell {
      padding: 8px 10px;
      border-right: 1px solid #e0e0e0;
      white-space: nowrap; /* No wrapping on desktop */
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
    }

    .div-table-cell:last-child {
      border-right: none;
    }

    .div-table-heading {
      color: #333;
      font-weight: 700; /* Consistent bold weight */
    }

    /* Column-specific styling */
    .product-column {
      flex: 3;
      min-width: 250px;
      max-width: 350px;
      justify-content: flex-start;
      padding-left: 12px;
    }

    .qty-column {
      flex: 0.5;
      min-width: 40px;
      max-width: 50px;
      justify-content: center;
      font-weight: 700; /* Make it bold */
      text-align: center;
      font-size: 15px; /* Increased from 13px to 15px (+2px) */
    }

    .price-column {
      flex: 1;
      min-width: 100px;
      justify-content: flex-end;
      padding-right: 12px;
      font-size: 15px; /* Increased from 13px to 15px (+2px) */
    }

    /* For the tax column that may be hidden */
    .tax-column {
      display: flex; /* Will be toggled with JavaScript */
    }

    /* Div Table Body */
    .div-table-body {
      display: flex;
      flex-direction: column;
    }

    /* Additional Comments Section */
    .additional-comments {
      margin-bottom: 20px;
    }

    .additional-comments h3 {
      margin-bottom: 10px;
      font-size: 20px;
    }

    .additional-comments p {
      font-size: 16px;
      white-space: pre-wrap;
    }

    /* Terms Section */
    .terms {
      text-align: left;
      margin-bottom: 20px;
    }

    .terms h3 {
      margin-bottom: 5px;
      font-size: 16px;
    }

    .terms p {
      font-size: 14px;
    }

    /* Totals Section */
    .totals {
      margin-top: 30px;
      padding: 20px;
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .totals p {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .totals p span {
      text-align: right;
      font-weight: bold;
    }

    .totals p.tax {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .totals p.tax span {
      text-align: right;
      font-weight: bold;
    }

    /* Footer */
    .footer {
      text-align: center;
      color: #666;
      font-size: 14px;
      margin-top: 30px;
    }

    /* Button Styles */
    .button-container {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 20px;
      width: 100%;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      padding: 0 10px;
    }

    .action-button {
      background-color: #4a90e2;
      color: white;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .action-button:hover {
      background-color: #357ABD;
    }

    .action-button:disabled {
      background-color: #aaa;
      cursor: not-allowed;
    }

    /* Loading Spinner Styles */
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 2s linear infinite;
      display: inline-block;
      vertical-align: middle;
      margin-left: 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* PDF Preview Styles (Optional) */
    #pdf-preview {
      margin-top: 20px;
      text-align: center;
    }

    #pdf-preview a {
      display: inline-block;
      margin-top: 10px;
      text-decoration: none;
      color: #007BFF;
    }

    /* Toast Container & Toast Styles */
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .toast {
      background-color: #333;
      color: #fff;
      padding: 10px 15px;
      border-radius: 5px;
      font-size: 14px;
      opacity: 0.95;
      animation: fadeInOut 3s forwards;
    }

    .toast.success {
      background-color: #28a745;
    }

    .toast.error {
      background-color: #dc3545;
    }

    .toast.info {
      background-color: #007bff;
    }

    @keyframes fadeInOut {
      0% { opacity: 0; }
      10% { opacity: 0.95; }
      90% { opacity: 0.95; }
      100% { opacity: 0; }
    }

    /* Responsive Design */
    @media (max-width: 830px) {
      body {
        padding: 10px;
      }

      .container {
        padding: 15px;
      }

      /* Adjust table for medium screens */
      .div-table-cell {
        padding: 6px 8px;
        font-size: 15px; /* Increased from 13px to 15px (+2px) */
      }

      /* Adjust column widths for medium screens */
      .product-column {
        min-width: 180px;
        flex: 2.5;
      }

      .qty-column {
        min-width: 35px;
        flex: 0.5;
        font-size: 14px; /* Increased from 12px to 14px (+2px) */
      }

      .price-column {
        min-width: 80px;
        flex: 1;
        font-size: 14px; /* Increased from 12px to 14px (+2px) */
      }

      /* Adjust header for medium screens */
      h1 {
        font-size: 28px;
      }

      .logo {
        width: 80px;
      }

      .logo img {
        max-height: 80px;
      }

      /* Adjust billing/shipping for medium screens */
      .billing, .shipping {
        font-size: 14px;
      }
    }

    /* Specific adjustments for small screens */
    @media (max-width: 650px) {
      body {
        padding: 5px;
      }

      .container {
        padding: 12px;
        box-shadow: 0 0 5px rgba(0,0,0,0.1);
      }

      /* Adjust header for small screens */
      h1 {
        font-size: 24px;
      }

      .logo {
        width: 60px;
      }

      .logo img {
        max-height: 60px;
      }

      /* Make the date smaller on mobile */
      #receipt-date {
        font-size: 12px;
      }

      /* Adjust info layout for small screens */
      .info-container {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        gap: 10px;
      }

      .company-details {
        flex: 1;
        font-size: 12px;
        padding-right: 10px;
      }

      .company-details h2 {
        font-size: 16px;
        margin-bottom: 5px;
      }

      .reference-number {
        font-size: 12px;
      }

      .reference-number-display {
        font-size: 12px;
      }

      .reference-number-input {
        width: 100px;
        font-size: 12px;
      }

      .ref-btn {
        font-size: 10px;
        min-width: 20px;
        height: 20px;
      }

      .address-contact p {
        font-size: 11px;
        line-height: 1.3;
      }

      .billing-shipping {
        flex: 1;
        flex-direction: column;
        font-size: 12px;
      }

      .billing, .shipping {
        flex-basis: 100%;
        margin-bottom: 10px;
        font-size: 11px;
      }

      .billing h3, .shipping h3 {
        font-size: 14px;
        margin-bottom: 5px;
      }

      .billing p, .shipping p {
        line-height: 1.3;
      }

      /* Adjust table for small screens */
      .table-container {
        overflow-x: visible; /* Disable horizontal scrolling on mobile */
        box-shadow: none;
        border-radius: 0;
      }

      .div-table {
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      .div-table-cell {
        padding: 5px 6px;
        font-size: 14px; /* Increased from 12px to 14px (+2px) */
      }

      .product-column {
        min-width: 0; /* Remove min-width to allow full flexibility */
        flex: 1.5;
      }

      .qty-column {
        min-width: 0; /* Remove min-width to allow full flexibility */
        max-width: none;
        flex: 0.5;
        font-size: 13px; /* Increased from 11px to 13px (+2px) */
      }

      .price-column {
        min-width: 0; /* Remove min-width to allow full flexibility */
        flex: 1;
        font-size: 13px; /* Increased from 11px to 13px (+2px) */
      }
    }

    /* Specific adjustments for very small screens */
    @media (max-width: 480px) {
      body {
        padding: 0;
      }

      .container {
        padding: 10px;
      }

      /* Make the date even smaller on very small screens */
      #receipt-date {
        font-size: 10px;
      }

      /* Further reduce text size for very small screens */
      .company-details {
        font-size: 10px;
      }

      .company-details h2 {
        font-size: 14px;
      }

      .reference-number {
        font-size: 10px;
      }

      .reference-number-display {
        font-size: 10px;
      }

      .reference-number-input {
        width: 90px;
        font-size: 10px;
      }

      .ref-btn {
        font-size: 9px;
        min-width: 18px;
        height: 18px;
      }

      .address-contact p {
        font-size: 9px;
      }

      .billing, .shipping {
        font-size: 10px;
      }

      .billing h3, .shipping h3 {
        font-size: 12px;
      }

      .billing p, .shipping p {
        font-size: 9px;
      }

      /* Adjust button container for very small screens */
      .button-container {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
      }

      .action-button {
        width: 100%;
        padding: 12px 0;
      }

      /* Mobile-optimized table */
      .div-table-cell {
        padding: 4px 5px;
        font-size: 13px; /* Increased from 11px to 13px (+2px) */
        white-space: normal; /* Allow text wrapping on mobile */
      }

      /* Adjust column proportions for mobile */
      .product-column {
        flex: 1.2;
      }

      .qty-column {
        flex: 0.4;
        font-size: 12px; /* Increased from 10px to 12px (+2px) */
      }

      .price-column {
        flex: 0.8;
        font-size: 12px; /* Increased from 10px to 12px (+2px) */
      }

      /* Adjust totals section for very small screens */
      .totals {
        padding: 10px;
        margin-top: 15px;
      }

      .totals p {
        font-size: 14px;
      }

      /* Adjust footer for very small screens */
      .footer {
        font-size: 12px;
        margin-top: 15px;
      }
    }

    /* Specific adjustments for extra small screens */
    @media (max-width: 380px) {
      .container {
        padding: 8px;
      }

      h1 {
        font-size: 20px;
      }

      .logo {
        width: 50px;
      }

      .logo img {
        max-height: 50px;
      }

      /* Further optimize table for very small screens */
      .div-table-cell {
        padding: 3px 4px;
        font-size: 10px;
      }

      /* Adjust column proportions for very small screens */
      .product-column {
        flex: 1;
      }

      .qty-column {
        flex: 0.3;
        justify-content: center;
        text-align: center;
      }

      .price-column {
        flex: 0.7;
      }

      /* Ensure the table fits the screen width */
      .div-table {
        width: 100%;
        min-width: 0;
      }
    }

    /* Ultra small screens */
    @media (max-width: 320px) {
      .container {
        padding: 5px;
      }

      .div-table-cell {
        padding: 2px 3px;
        font-size: 11px; /* Increased from 9px to 11px (+2px) */
      }

      /* Make the date tiny on ultra small screens */
      #receipt-date {
        font-size: 9px;
      }

      /* Extreme size reduction for ultra small screens */
      .company-details {
        font-size: 9px;
      }

      .company-details h2 {
        font-size: 12px;
      }

      .reference-number {
        font-size: 9px;
      }

      .reference-number-display {
        font-size: 9px;
      }

      .reference-number-input {
        width: 80px;
        font-size: 9px;
      }

      .ref-btn {
        font-size: 8px;
        min-width: 16px;
        height: 16px;
      }

      .address-contact p {
        font-size: 8px;
      }

      .billing, .shipping {
        font-size: 9px;
      }

      .billing h3, .shipping h3 {
        font-size: 11px;
      }

      .billing p, .shipping p {
        font-size: 8px;
      }

      /* Add a bit more spacing between the two sections */
      .info-container {
        gap: 5px;
      }
    }
  </style>
</head>
<body>
  <!-- Toast Container -->
  <div class="toast-container" id="toastContainer"></div>

  <div style="display: flex; margin: 20px;">
    <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px; margin-right: 20px; display: inline-block;">&larr; Back to Home</a>
    <a href="javascript:void(0)" id="edit-order-button" style="text-decoration: none; color: #007BFF; font-size: 16px; display: inline-block;">✏️ Edit Order</a>
  </div>

  <!-- Container wrapper for scaling -->
  <div class="container-wrapper">
    <div id="pdf-1" class="container">
      <div id="receipt">
        <div class="header">
          <div class="date-section">
            <span id="receipt-date-display" class="date-display">Date: <!-- Dynamic Date --></span>
            <button id="edit-date-btn" class="date-edit-btn" onclick="editDate()">Edit</button>
            <div id="date-edit-section" class="date-edit-controls" style="display: none;">
              <input type="date" id="date-input" class="date-input">
              <div class="date-edit-buttons">
                <button onclick="saveDateEdit()" class="date-save-btn">Save</button>
                <button onclick="cancelDateEdit()" class="date-cancel-btn">Cancel</button>
              </div>
            </div>
          </div>
          <h1>RECEIPT</h1>
          <div class="logo">
            <img src="./Assets/logo2.png" alt="Company Logo">
          </div>
        </div>

      <div class="info-container">
        <div class="company-details">
          <h2 id="company-name">Shans Accessories PTY LTD</h2>
          <p>61 Civin Drive<br>Bedfordview<br>Johannesburg</p>
          <div class="address-contact">
            <p id="banking-info"></p>
          </div>
          <div class="reference-number-container">
            <span class="reference-number-display" id="reference-number-display">Reference Number: <!-- Dynamic Reference --></span>
            <div class="reference-number-edit" id="reference-number-edit" style="display: none;">
              <label for="reference-number-input">Reference Number:</label>
              <input type="text" id="reference-number-input" class="reference-number-input" maxlength="20" />
              <button type="button" id="reference-save-btn" class="ref-btn ref-save-btn">✓</button>
              <button type="button" id="reference-cancel-btn" class="ref-btn ref-cancel-btn">✗</button>
            </div>
            <button type="button" id="reference-edit-btn" class="ref-btn ref-edit-btn">✏️</button>
          </div>
        </div>

        <div class="billing-shipping">
          <div class="billing">
            <h3>BILL TO</h3>
            <p><!-- Customer Billing Information --></p>
          </div>
          <div class="shipping">
            <h3>SHIP TO</h3>
            <p><!-- Customer Shipping Information --></p>
          </div>
        </div>
      </div>

      <p class="payment-method">Payment Method: <span id="paymentMethod">N/A</span></p>

      <!-- Products Table (Div-based) -->
      <div class="table-container">
        <div class="div-table">
          <!-- Table Header -->
          <div class="div-table-row div-table-header" id="table-header">
            <div class="div-table-cell div-table-heading product-column">Product</div>
            <div class="div-table-cell div-table-heading qty-column" style="font-weight: 700; text-align: center;">QTY</div>
            <div class="div-table-cell div-table-heading price-column">PRICE</div>
            <div class="div-table-cell div-table-heading price-column tax-column">TAX</div>
            <div class="div-table-cell div-table-heading price-column">TOTAL</div>
          </div>
          <!-- Table Body - Dynamic Product Rows Will Be Inserted Here -->
          <div class="div-table-body">
            <!-- JavaScript will populate this section -->
          </div>
        </div>
      </div>

      <!-- Additional Comments Section -->
      <div id="additional-comments" class="additional-comments" style="margin-bottom: 20px;">
        <!-- Additional Comments will be inserted here if available -->
      </div>

      <!-- Terms Section -->
      <div class="terms">
        <h3>Terms</h3>
        <p>No refunds or exchanges on correctly supplied items</p>
      </div>

      <!-- Totals Section -->
      <div class="totals">
        <p class="subtotal">Product Amount (Subtotal): <span id="subtotalAmount">R0.00</span></p>
        <p class="tax" id="tax-info" style="display: none;">Tax (15%): <span id="taxAmount">R0.00</span></p>
        <p class="total">Total Amount: <span id="totalAmount">R0.00</span></p>
      </div>
    </div>

    <div class="footer">
      <p>Follow us to see more on</p>
      <p>Instagram: @shans_car_accessories</p>
      <p>Twitter/Pinterest: Shans Accessories</p>
      <p>Facebook: Shans Accessories (By Car Brand)</p>
      <p>YouTube/Tik Tok: Shans Accessories All products are Non OEM</p>
    </div>
    </div>
  </div><!-- End of container-wrapper -->

  <div class="button-container">
    <button id="confirm-button" class="action-button">Confirm</button>
    <!-- Screenshot Button -->
    <button id="screenshot-button" class="action-button">Download Image</button>
    <div id="loading-spinner" class="spinner" style="display: none;"></div>
  </div>

  <div id="pdf-preview"></div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <!-- Include html2canvas for image capturing -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

  <script>
    // Main initialization function
    function initializeReceiptPage() {
      // Clear any existing receipt cookies when the page loads
      // We don't want to clear these cookies if they were just set
      // This is just a safeguard for when users navigate back to this page
      // after having already processed a receipt
      const urlParams = new URLSearchParams(window.location.search);
      const skipClearCookies = urlParams.get('skipClearCookies');

      if (skipClearCookies !== 'true') {
        // Only clear the receiptConfirmed cookie, not the actual receipt data
        document.cookie = 'receiptConfirmed=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      }

      // Populate the receipt with data
      populateReceipt();

      // Set up event listeners
      const confirmButton = document.getElementById('confirm-button');
      if (confirmButton) {
        confirmButton.removeEventListener('click', confirmReceipt);
        confirmButton.addEventListener('click', confirmReceipt);
      }

      const screenshotButton = document.getElementById('screenshot-button');
      if (screenshotButton) {
        screenshotButton.removeEventListener('click', captureScreenshot);
        screenshotButton.addEventListener('click', captureScreenshot);
      }
    }

    // Function to handle the edit order button click
    function handleEditOrder() {
      // Set a flag in sessionStorage to indicate we're editing an existing order
      sessionStorage.setItem('editingOrder', 'true');

      // Redirect back to the index page
      window.location.href = 'index.html';
    }

    // Initialize everything when the DOM is ready
    document.addEventListener('DOMContentLoaded', async function() {
      // Check authentication first
      const isAuthenticated = await initAuth({ requireAuth: true, showLoading: false });
      if (!isAuthenticated) {
        return; // initAuth will handle the redirect
      }

      initializeReceiptPage();

      // Add event listener for the edit order button
      const editOrderButton = document.getElementById('edit-order-button');
      if (editOrderButton) {
        editOrderButton.addEventListener('click', handleEditOrder);
      }
    });

    /**
     * Display a non-blocking toast message in the top-right corner.
     * @param {string} message - The message to display
     * @param {string} [type='info'] - The type of toast ('success', 'error', 'info')
     */
    function showToastMessage(message, type = 'info') {
      const toastContainer = document.getElementById('toastContainer');
      const toast = document.createElement('div');
      toast.classList.add('toast', type);
      toast.textContent = message;
      toastContainer.appendChild(toast);
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }

    // Function to retrieve a cookie by name
    function getCookie(name) {
      const cname = name + "=";
      const decodedCookie = decodeURIComponent(document.cookie);
      const ca = decodedCookie.split(';');
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(cname) === 0) {
          return c.substring(cname.length, c.length);
        }
      }
      return "";
    }

    // Function to set a cookie
    function setCookie(name, value, days = 1) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      const expires = `expires=${date.toUTCString()}`;
      document.cookie = `${name}=${value};${expires};path=/`;
    }

    // Function to format currency in ZAR (no decimals)
    function formatCurrency(amount) {
      return 'R' + parseFloat(amount).toLocaleString('en-ZA', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });
    }

    // Function to sanitize HTML to prevent XSS
    function sanitizeHTML(str) {
      const temp = document.createElement('div');
      temp.textContent = str;
      return temp.innerHTML;
    }

    // Function to format date
    function formatDate(date) {
      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      return new Intl.DateTimeFormat('en-US', options).format(date);
    }

    // Function to generate a random 7-digit reference number
    function generateReferenceNumber() {
      return Math.floor(1000000 + Math.random() * 9000000).toString();
    }

    // Reference Number Management Functions
    let currentReferenceNumber = '';

    // Date Management Functions
    let currentDate = new Date().toISOString().slice(0, 10); // YYYY-MM-DD format

    function initializeReferenceNumber(referenceNumber) {
      currentReferenceNumber = referenceNumber;
      updateReferenceNumberDisplay();
      setupReferenceNumberEventListeners();
    }

    function updateReferenceNumberDisplay() {
      const displayElement = document.getElementById('reference-number-display');
      displayElement.textContent = 'Reference Number: ' + currentReferenceNumber;
    }

    function setupReferenceNumberEventListeners() {
      const editBtn = document.getElementById('reference-edit-btn');
      const saveBtn = document.getElementById('reference-save-btn');
      const cancelBtn = document.getElementById('reference-cancel-btn');
      const input = document.getElementById('reference-number-input');

      editBtn.addEventListener('click', startEditingReferenceNumber);
      saveBtn.addEventListener('click', saveReferenceNumber);
      cancelBtn.addEventListener('click', cancelEditingReferenceNumber);

      // Allow saving with Enter key
      input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          saveReferenceNumber();
        } else if (e.key === 'Escape') {
          cancelEditingReferenceNumber();
        }
      });
    }

    function startEditingReferenceNumber() {
      const displayElement = document.getElementById('reference-number-display');
      const editElement = document.getElementById('reference-number-edit');
      const editBtn = document.getElementById('reference-edit-btn');
      const input = document.getElementById('reference-number-input');

      displayElement.style.display = 'none';
      editBtn.style.display = 'none';
      editElement.style.display = 'flex';

      input.value = currentReferenceNumber;
      input.focus();
      input.select();
    }

    function saveReferenceNumber() {
      const input = document.getElementById('reference-number-input');
      const newReference = input.value.trim();

      if (newReference && newReference !== currentReferenceNumber) {
        currentReferenceNumber = newReference;
        updateReferenceNumberDisplay();

        // Update the customer info cookie with the new reference number
        const customerInfoCookie = getCookie('customerInfo');
        if (customerInfoCookie) {
          const customerInfo = JSON.parse(customerInfoCookie);
          customerInfo.referenceNumber = newReference;
          setCookie('customerInfo', JSON.stringify(customerInfo));
        }
      }

      cancelEditingReferenceNumber();
    }

    function cancelEditingReferenceNumber() {
      const displayElement = document.getElementById('reference-number-display');
      const editElement = document.getElementById('reference-number-edit');
      const editBtn = document.getElementById('reference-edit-btn');

      editElement.style.display = 'none';
      displayElement.style.display = 'inline';
      editBtn.style.display = 'inline-flex';
    }

    function getCurrentReferenceNumber() {
      return currentReferenceNumber;
    }

    // Date editing functions
    function editDate() {
      const editBtn = document.getElementById('edit-date-btn');
      const editSection = document.getElementById('date-edit-section');
      const dateInput = document.getElementById('date-input');

      editBtn.style.display = 'none';
      editSection.style.display = 'block';
      dateInput.value = currentDate;
      dateInput.focus();
    }

    function saveDateEdit() {
      const dateInput = document.getElementById('date-input');
      const newDate = dateInput.value;

      if (newDate) {
        currentDate = newDate;
        updateDateDisplay();
      }

      cancelDateEdit();
    }

    function cancelDateEdit() {
      const editBtn = document.getElementById('edit-date-btn');
      const editSection = document.getElementById('date-edit-section');

      editBtn.style.display = 'inline-block';
      editSection.style.display = 'none';
    }

    function updateDateDisplay() {
      const displayElement = document.getElementById('receipt-date-display');
      const dateObj = new Date(currentDate + 'T00:00:00'); // Add time to avoid timezone issues
      displayElement.textContent = 'Date: ' + formatDate(dateObj);
    }

    function getCurrentDate() {
      return currentDate;
    }

    /**
     * Populate the receipt with data from cookies, applying the "tax = 15% of final price" logic:
     *   If tax is added, then for each product price P:
     *     - Net (Unit Price) = P * 0.85
     *     - Tax per Unit = P * 0.15
     *     - Total per line = P * quantity
     *   If no tax, we simply show P as the unit price, 0 for tax, and total = P * quantity.
     */
    function populateReceipt() {
      const customerInfoCookie = getCookie('customerInfo');
      const selectedProductsCookie = getCookie('selectedProducts');
      const subtotalAmountCookie = getCookie('subtotalAmount');
      const taxAmountCookie = getCookie('taxAmount');
      const totalAmountCookie = getCookie('totalAmount');
      const selectedCompanyCookie = getCookie('selectedCompany');

      if (!customerInfoCookie ||
          !selectedProductsCookie ||
          !subtotalAmountCookie ||
          !taxAmountCookie ||
          !totalAmountCookie ||
          !selectedCompanyCookie) {
        showToastMessage('No receipt data found. Please generate a receipt first.', 'error');
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1500);
        return;
      }

      const customerInfo = JSON.parse(customerInfoCookie);
      const selectedProducts = JSON.parse(selectedProductsCookie);
      const subtotalAmount = parseFloat(JSON.parse(subtotalAmountCookie));
      const taxAmount = parseFloat(JSON.parse(taxAmountCookie));
      const totalAmount = parseFloat(JSON.parse(totalAmountCookie));
      const selectedCompany = JSON.parse(selectedCompanyCookie);

      document.getElementById('company-name').textContent = selectedCompany.name;
      document.getElementById('banking-info').innerHTML = selectedCompany.bankingInformation;

      // Initialize reference number
      const generatedReference = generateReferenceNumber();
      initializeReferenceNumber(generatedReference);

      // Populate Billing
      const billingElement = document.querySelector('.billing p');
      billingElement.innerHTML = `
        <strong>${sanitizeHTML(customerInfo.billing.name)}</strong><br>
        ${sanitizeHTML(customerInfo.billing.address)}<br>
        Email: ${sanitizeHTML(customerInfo.billing.email)}<br>
        Phone: ${sanitizeHTML(customerInfo.billing.phone)}
      `;

      // Populate Shipping
      const shippingElement = document.querySelector('.shipping p');
      if (customerInfo.shipping) {
        shippingElement.innerHTML = `
          <strong>${sanitizeHTML(customerInfo.shipping.name)}</strong><br>
          ${sanitizeHTML(customerInfo.shipping.address)}<br>
          Email: ${sanitizeHTML(customerInfo.shipping.email)}<br>
          Phone: ${sanitizeHTML(customerInfo.shipping.phone)}
        `;
      } else {
        shippingElement.innerHTML = `Same as billing address`;
      }

      // Determine if tax is actually added by checking if taxAmount > 0
      const isTaxAdded = (taxAmount > 0);
      const tableHeaderRow = document.getElementById('table-header');
      const taxInfo = document.getElementById('tax-info');

      // If there's any tax, show the "TAX PER UNIT" column, otherwise hide it
      const taxColumn = document.querySelector('.tax-column');
      if (isTaxAdded) {
        // Show the tax column
        taxColumn.style.display = 'flex';
        taxInfo.style.display = 'flex';
      } else {
        // Hide the tax column
        taxColumn.style.display = 'none';
        taxInfo.style.display = 'none';
      }

      // Now populate the table rows
      const tableBody = document.querySelector('.div-table-body');
      tableBody.innerHTML = '';

      selectedProducts.forEach(product => {
        // Create a new row
        const row = document.createElement('div');
        row.className = 'div-table-row';

        // Product name cell
        const descCell = document.createElement('div');
        descCell.className = 'div-table-cell product-column';
        descCell.textContent = product.name;
        row.appendChild(descCell);

        // Quantity cell
        const qtyCell = document.createElement('div');
        qtyCell.className = 'div-table-cell qty-column';
        qtyCell.textContent = product.quantity;
        qtyCell.style.fontWeight = '700'; // Ensure QTY is bold
        qtyCell.style.textAlign = 'center'; // Ensure text is centered
        row.appendChild(qtyCell);

        // We'll apply the same "tax = 15% of final price" logic used in the main code
        if (isTaxAdded) {
          // net = price * 0.85, tax = price * 0.15, total = price * quantity
          const netPrice = product.price * 0.85;
          const taxPerUnit = product.price * 0.15;
          const lineTotal = product.price * product.quantity;

          // Unit Price cell
          const unitPriceCell = document.createElement('div');
          unitPriceCell.className = 'div-table-cell price-column';
          unitPriceCell.textContent = formatCurrency(netPrice.toFixed(2));
          row.appendChild(unitPriceCell);

          // Tax cell
          const taxCell = document.createElement('div');
          taxCell.className = 'div-table-cell price-column tax-column';
          taxCell.textContent = formatCurrency(taxPerUnit.toFixed(2));
          row.appendChild(taxCell);

          // Total cell
          const totalCell = document.createElement('div');
          totalCell.className = 'div-table-cell price-column';
          totalCell.textContent = formatCurrency(lineTotal.toFixed(2));
          row.appendChild(totalCell);
        } else {
          // No tax => unit price is the full product.price
          // total = price * quantity
          const lineTotal = product.price * product.quantity;

          // Unit Price cell
          const unitPriceCell = document.createElement('div');
          unitPriceCell.className = 'div-table-cell price-column';
          unitPriceCell.textContent = formatCurrency(product.price.toFixed(2));
          row.appendChild(unitPriceCell);

          // Total cell (no tax column)
          const totalCell = document.createElement('div');
          totalCell.className = 'div-table-cell price-column';
          totalCell.textContent = formatCurrency(lineTotal.toFixed(2));
          row.appendChild(totalCell);
        }

        // Add the row to the table body
        tableBody.appendChild(row);
      });

      // Additional Comments
      const comments = customerInfo.comments;
      const commentsDiv = document.getElementById('additional-comments');
      if (comments && comments.trim() !== "") {
        commentsDiv.innerHTML = `
          <h3>Additional Comments:</h3>
          <p>${sanitizeHTML(comments).replace(/\n/g, '<br>')}</p>
        `;
        commentsDiv.style.display = 'block';
      } else {
        commentsDiv.style.display = 'none';
      }

      // Fill in the Subtotal, Tax, and Total
      document.getElementById('subtotalAmount').textContent = formatCurrency(subtotalAmount.toFixed(0));
      document.getElementById('taxAmount').textContent = formatCurrency(taxAmount.toFixed(0));
      document.getElementById('totalAmount').textContent = formatCurrency(totalAmount.toFixed(0));

      // Payment Method
      const paymentMethodSpan = document.getElementById('paymentMethod');
      paymentMethodSpan.textContent = customerInfo.paymentMethod ? sanitizeHTML(customerInfo.paymentMethod) : 'N/A';

      // Date - initialize and display
      updateDateDisplay();
    }

    // Function to confirm and send the receipt
    async function confirmReceipt() {
      const confirmButton = document.getElementById('confirm-button');
      const loadingSpinner = document.getElementById('loading-spinner');
      const pdfPreview = document.getElementById('pdf-preview');

      // Check if there's already a success message displayed (indicating a previous successful submission)
      const existingSuccessMessage = document.querySelector('.success-message');
      if (existingSuccessMessage) {
        showToastMessage('This receipt has already been processed.', 'info');
        return;
      }

      // Disable the button but don't hide it yet
      confirmButton.disabled = true;
      loadingSpinner.style.display = 'inline-block';
      pdfPreview.innerHTML = '';

      try {
        const customerInfoCookie = getCookie('customerInfo');
        const selectedProductsCookie = getCookie('selectedProducts');
        const subtotalAmountCookie = getCookie('subtotalAmount');
        const taxAmountCookie = getCookie('taxAmount');
        const totalAmountCookie = getCookie('totalAmount');
        const selectedCompanyCookie = getCookie('selectedCompany');

        if (!customerInfoCookie ||
            !selectedProductsCookie ||
            !subtotalAmountCookie ||
            !taxAmountCookie ||
            !totalAmountCookie ||
            !selectedCompanyCookie) {
          throw new Error('No receipt data found.');
        }

        const customerInfo = JSON.parse(customerInfoCookie);
        const selectedProducts = JSON.parse(selectedProductsCookie);
        const subtotalAmount = parseFloat(JSON.parse(subtotalAmountCookie));
        const taxAmount = parseFloat(JSON.parse(taxAmountCookie));
        const totalAmount = parseFloat(JSON.parse(totalAmountCookie));
        const selectedCompany = JSON.parse(selectedCompanyCookie);

        // Debug: Check what's in selectedProducts from cookies
        console.log('[COOKIE DEBUG] selectedProducts from cookies:', selectedProducts);
        selectedProducts.forEach(product => {
          console.log(`[COOKIE DEBUG] Product ${product.name} - unit_cost: ${product.unit_cost}, price: ${product.price}`);
        });

        // Use the current reference number (which may have been edited by the user)
        const finalReferenceNumber = getCurrentReferenceNumber();

        // Update the display with the final reference number
        updateReferenceNumberDisplay();

        // Construct the object to send to the backend
        // We'll apply the same net=85% / tax=15% logic if taxAmount>0
        const isTaxAdded = (taxAmount > 0);

        const receiptData = {
          date: getCurrentDate(),
          referenceNumber: finalReferenceNumber,
          company: {
            name: selectedCompany.name,
            bankingInformation: selectedCompany.bankingInformation
          },
          billing: customerInfo.billing,
          shipping: customerInfo.shipping || null,
          paymentMethod: customerInfo.paymentMethod || 'N/A',
          comments: customerInfo.comments || "",
          salespersonName: customerInfo.salespersonName || "",
          products: selectedProducts.map(product => {
            // Get unit cost from product, price is the selling price
            const unitCost = product.unit_cost || 0;
            const sellingPrice = product.price;

            // Debug: Log unit cost being sent to backend
            console.log(`[RECEIPT DEBUG] Product ${product.name} - unit_cost: ${unitCost}, selling_price: ${sellingPrice}`);

            if (isTaxAdded) {
              // net = price * 0.85, tax = price * 0.15, total = price * quantity
              // Use the negotiated price from the frontend (product.price)
              const netPrice = parseFloat((product.price * 0.85).toFixed(2));
              const taxPerUnit = parseFloat((product.price * 0.15).toFixed(2));
              const lineTotal = parseFloat((product.price * product.quantity).toFixed(2));
              // Calculate profit as the difference between the selling price and unit cost
              const profitPerUnit = sellingPrice - unitCost;
              const totalProfit = profitPerUnit * product.quantity;

              return {
                item_code: product.item_code,
                name: product.name,
                quantity: product.quantity,
                unitPriceExcludingTax: netPrice,
                unit_cost: unitCost,
                profit_per_unit: profitPerUnit,
                total_profit: totalProfit,
                taxPerProduct: taxPerUnit,
                totalPrice: lineTotal,
                room_id: product.room_id,
                room_name: product.room_name
              };
            } else {
              // no tax
              // Use the negotiated price from the frontend (product.price)
              const unitPrice = parseFloat(product.price.toFixed(2));
              const lineTotal = parseFloat((product.price * product.quantity).toFixed(2));
              // Calculate profit as the difference between the selling price and unit cost
              const profitPerUnit = sellingPrice - unitCost;
              const totalProfit = profitPerUnit * product.quantity;

              return {
                item_code: product.item_code,
                name: product.name,
                quantity: product.quantity,
                unitPriceExcludingTax: unitPrice,
                unit_cost: unitCost,
                profit_per_unit: profitPerUnit,
                total_profit: totalProfit,
                taxPerProduct: 0.00,
                totalPrice: lineTotal,
                room_id: product.room_id,
                room_name: product.room_name
              };
            }
          }),
          subtotal: parseFloat(subtotalAmount.toFixed(2)),
          tax: parseFloat(taxAmount.toFixed(2)),
          total: parseFloat(totalAmount.toFixed(2))
        };

        console.log('Sending receipt data to backend:', receiptData);

        // Make the POST request to your PDF generation endpoint
        const response = await fetch('http://localhost:8000/api/generate-pdf', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(receiptData)
        });

        if (!response.ok) {
          let errorMessage = 'Failed to generate PDF.';
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch (e) {
            console.error('Error parsing error response:', e);
          }
          throw new Error(errorMessage);
        }

        const successData = await response.json();
        console.log('Backend response:', successData);

        // Now that the request is successful, hide the confirm button
        confirmButton.style.display = 'none';

        showToastMessage('Receipt successfully processed and sent!', 'success');
        // Add a more visible success message
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message'; // Add a class for identification
        successMessage.style.textAlign = 'center';
        successMessage.style.margin = '20px 0';
        successMessage.style.padding = '15px';
        successMessage.style.backgroundColor = '#d4edda';
        successMessage.style.color = '#155724';
        successMessage.style.borderRadius = '5px';
        successMessage.style.fontWeight = 'bold';
        successMessage.innerHTML = 'Receipt has been successfully processed.<br>You can now download the image or return to the home page.';

        // Insert the message after the button container
        const buttonContainer = document.querySelector('.button-container');
        buttonContainer.parentNode.insertBefore(successMessage, buttonContainer.nextSibling);
      } catch (error) {
        console.error('Error generating PDF:', error);
        showToastMessage(`Error: ${error.message}`, 'error');

        // Re-enable the button so the user can try again
        confirmButton.disabled = false;
      } finally {
        // Stop the spinner
        loadingSpinner.style.display = 'none';
      }
    }

    // Define screenshot function
    function captureScreenshot() {
      const loadingSpinner = document.getElementById('loading-spinner');
      loadingSpinner.style.display = 'inline-block';
      const element = document.getElementById('pdf-1');

      // Hide all edit buttons before screenshot
      const editButtons = element.querySelectorAll('.date-edit-btn, .date-edit-controls, .ref-edit-btn, #reference-number-edit');
      const originalDisplayStates = [];
      editButtons.forEach((btn, index) => {
        originalDisplayStates[index] = btn.style.display;
        btn.style.display = 'none';
      });

      // Temporarily remove any scaling for the screenshot
      const originalTransform = element.style.transform;
      element.style.transform = 'none';

      // Get client name and reference number for the filename
      const customerInfoCookie = getCookie('customerInfo');
      let clientName = 'client';
      if (customerInfoCookie) {
        const customerInfo = JSON.parse(customerInfoCookie);
        if (customerInfo.billing && customerInfo.billing.name) {
          // Replace spaces with underscores and remove special characters
          clientName = customerInfo.billing.name.replace(/[^a-zA-Z0-9]/g, '_').replace(/_+/g, '_');
        }
      }

      // Get reference number from current state
      const referenceNumber = getCurrentReferenceNumber() || 'ref';

      // Create dynamic filename
      const filename = `receipt-${clientName}-${referenceNumber}.png`;

      // Use higher scale for better quality
      html2canvas(element, {
        scale: 2,
        width: 794, // A4 width
        height: 1123, // A4 height
        windowWidth: 794,
        windowHeight: 1123
      })
        .then(canvas => {
          const imgData = canvas.toDataURL('image/png');
          const link = document.createElement('a');
          link.href = imgData;
          link.download = filename;
          link.click();

          // Restore original transform
          element.style.transform = originalTransform;

          // Restore edit buttons visibility
          editButtons.forEach((btn, index) => {
            btn.style.display = originalDisplayStates[index];
          });

          loadingSpinner.style.display = 'none';
          showToastMessage(`Image saved as ${filename}`, 'success');
        })
        .catch(err => {
          console.error('Error capturing image:', err);
          showToastMessage('Failed to capture image.', 'error');

          // Restore original transform even on error
          element.style.transform = originalTransform;

          // Restore edit buttons visibility even on error
          editButtons.forEach((btn, index) => {
            btn.style.display = originalDisplayStates[index];
          });

          loadingSpinner.style.display = 'none';
        });
    }

    // End of script
  </script>
</body>
</html>
